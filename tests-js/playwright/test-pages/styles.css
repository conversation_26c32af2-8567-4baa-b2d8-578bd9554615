body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #fafafa;
}

.section.success {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.section.info {
    background-color: #e7f3ff;
    color: #004085;
    border-color: #b3d7ff;
}

button {
    background-color: #007bff;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin: 10px 5px;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #0056b3;
}

.btn-danger {
    background-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
}

.status {
    margin: 15px 0;
    padding: 10px;
    border-radius: 4px;
    font-weight: bold;
}

.status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background-color: #e7f3ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

#tokenStatus {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin: 10px 0;
    font-family: monospace;
    font-size: 14px;
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

h3 {
    color: #495057;
    margin-top: 0;
}

p {
    line-height: 1.6;
    color: #6c757d;
}

strong {
    color: #495057;
}
