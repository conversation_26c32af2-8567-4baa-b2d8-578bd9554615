// OAuth utility functions

// PKCE helper functions
function generateRandomString(length) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return result;
}

async function generateCodeChallenge(codeVerifier) {
    const encoder = new TextEncoder();
    const data = encoder.encode(codeVerifier);
    const digest = await crypto.subtle.digest('SHA-256', data);
    return btoa(String.fromCharCode(...new Uint8Array(digest)))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}

// OAuth flow functions
async function startOAuthFlow() {
    try {
        showStatus('Preparing OAuth flow...', 'info');
        
        const codeVerifier = generateRandomString(128);
        const codeChallenge = await generateCodeChallenge(codeVerifier);
        const state = generateRandomString(32);

        localStorage.setItem('oauth_code_verifier', codeVerifier);
        localStorage.setItem('oauth_state', state);
        localStorage.setItem('oauth_timestamp', Date.now().toString());

        const config = window.OAUTH_TEST_CONFIG;
        const redirectUri = `http://test-client.localhost:6060/auth/callback/`;

        const authUrl = new URL(`${config.authUrl}/realms/${config.authRealm}/protocol/openid-connect/auth`);
        authUrl.searchParams.set('client_id', config.thirdPartyClientId);
        authUrl.searchParams.set('redirect_uri', redirectUri);
        authUrl.searchParams.set('response_type', 'code');
        authUrl.searchParams.set('scope', 'openid profile email offline_access');
        authUrl.searchParams.set('state', state);
        authUrl.searchParams.set('code_challenge', codeChallenge);
        authUrl.searchParams.set('code_challenge_method', 'S256');

        console.log('Redirecting to:', authUrl.toString());
        showStatus('Redirecting to authentication server...', 'success');
        
        window.location.href = authUrl.toString();
        
    } catch (error) {
        console.error('OAuth flow error:', error);
        showStatus('Error starting OAuth flow: ' + error.message, 'error');
    }
}

async function exchangeCodeForToken(code, codeVerifier) {
    const config = window.OAUTH_TEST_CONFIG;
    const tokenUrl = `${config.authUrl}/realms/${config.authRealm}/protocol/openid-connect/token`;
    
    const params = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: config.thirdPartyClientId,
        client_secret: config.thirdPartyClientSecret,
        code: code,
        redirect_uri: `http://test-client.localhost:6060/auth/callback/`,
        code_verifier: codeVerifier
    });

    const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params
    });

    const responseText = await response.text();

    if (!response.ok) {
        throw new Error(`Token exchange failed: ${response.status} - ${responseText}`);
    }

    return JSON.parse(responseText);
}

// Utility functions
function showStatus(message, type = 'info') {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
}

function clearStorage() {
    localStorage.clear();
    updateTokenStatus();
    showStatus('Storage cleared', 'success');
}

async function testApiCall() {
    const token = localStorage.getItem('access_token');
    if (!token) {
        showStatus('No token available for API test', 'error');
        return;
    }

    try {
        showStatus('Testing API call with cross-client token...', 'info');
        
        const config = window.OAUTH_TEST_CONFIG;
        const response = await fetch(`${config.serverUrl}/ping`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            showStatus('✅ API call successful! Cross-client token exchange worked!', 'success');
        } else {
            showStatus(`❌ API call failed: ${response.status} ${response.statusText}`, 'error');
        }
    } catch (error) {
        showStatus('❌ API call error: ' + error.message, 'error');
    }
}

function updateTokenStatus() {
    const token = localStorage.getItem('access_token');
    const statusDiv = document.getElementById('tokenStatus');
    
    if (token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const exp = new Date(payload.exp * 1000);
            const isExpired = Date.now() > payload.exp * 1000;
            
            statusDiv.innerHTML = 
                `<strong>Token Status:</strong> ${isExpired ? 'EXPIRED' : 'VALID'}<br>` +
                `<strong>Expires:</strong> ${exp.toLocaleString()}<br>` +
                `<strong>Client:</strong> ${payload.azp || 'Unknown'}<br>` +
                `<strong>Subject:</strong> ${payload.sub || 'Unknown'}`;
        } catch (e) {
            statusDiv.textContent = 'Invalid token stored';
        }
    } else {
        statusDiv.textContent = 'No token stored';
    }
}
