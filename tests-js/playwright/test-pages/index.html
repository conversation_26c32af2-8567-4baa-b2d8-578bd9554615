<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Cross-Client Token Exchange Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>🔄 OAuth Cross-Client Token Exchange Test</h1>
        
        <div class="section info">
            <h3>Test Configuration</h3>
            <p><strong>Third-party client:</strong> <span id="thirdPartyClientId">Loading...</span></p>
            <p><strong>Our app client:</strong> <span id="appClientId">Loading...</span></p>
            <p><strong>Server URL:</strong> <span id="serverUrl">Loading...</span></p>
            <p><strong>Redirect URI:</strong> <span id="redirectUri">Loading...</span></p>
        </div>

        <div class="section">
            <h3>Step 1: Authenticate with Third-Party Client</h3>
            <p>This will start OAuth flow with the third-party client. The token will then be exchanged by our server.</p>
            <button id="loginBtn" onclick="startOAuthFlow()">Start OAuth Flow</button>
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>Current Status</h3>
            <div id="tokenStatus">No token stored</div>
            <button onclick="clearStorage()" class="btn-danger">Clear Storage</button>
            <button onclick="testApiCall()" class="btn-success">Test API Call</button>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="oauth-utils.js"></script>
    <script src="main.js"></script>
</body>
</html>
