/* tslint:disable */
/* eslint-disable */

/* auto-generated by NAPI-RS */

/** Flexible configuration options for the Bodhi server that can be passed across NAPI boundary */
export interface NapiAppOptions {
  /** Environment variables to set */
  envVars: Record<string, string>
  /** App settings (configurable via settings.yaml) */
  appSettings: Record<string, string>
  /** System settings (immutable) */
  systemSettings: Record<string, string>
  /** OAuth client ID (optional) */
  clientId?: string
  /** OAuth client secret (optional) */
  clientSecret?: string
  /** App status as string (optional) */
  appStatus?: string
}
/** Create a new NapiAppOptions with empty configuration */
export declare function createNapiAppOptions(): NapiAppOptions
/** Set an environment variable */
export declare function setEnvVar(config: NapiAppOptions, key: string, value: string): NapiAppOptions
/** Set an app setting (configurable via settings.yaml) */
export declare function setAppSetting(config: NapiAppOptions, key: string, value: string): NapiAppOptions
/** Set a system setting (immutable) */
export declare function setSystemSetting(config: NapiAppOptions, key: string, value: string): NapiAppOptions
/** Set OAuth client credentials */
export declare function setClientCredentials(config: NapiAppOptions, clientId: string, clientSecret: string): NapiAppOptions
/** Set app status */
export declare function setAppStatus(config: NapiAppOptions, status: string): NapiAppOptions
export const BODHI_HOME: string
export const BODHI_HOST: string
export const BODHI_PORT: string
export const BODHI_SCHEME: string
export const BODHI_LOG_LEVEL: string
export const BODHI_LOG_STDOUT: string
export const BODHI_LOGS: string
export const BODHI_ENV_TYPE: string
export const BODHI_APP_TYPE: string
export const BODHI_VERSION: string
export const BODHI_AUTH_URL: string
export const BODHI_AUTH_REALM: string
export const BODHI_ENCRYPTION_KEY: string
export const BODHI_EXEC_LOOKUP_PATH: string
export const BODHI_EXEC_VARIANT: string
export const BODHI_KEEP_ALIVE_SECS: string
export const HF_HOME: string
export const DEFAULT_HOST: string
export const DEFAULT_PORT: number
/** The main Bodhi server wrapper for NAPI */
export declare class BodhiServer {
  /** Create a new Bodhi server instance with the given configuration */
  constructor(config: NapiAppOptions)
  /** Get the server configuration */
  get config(): NapiAppOptions
  /** Get the server URL */
  serverUrl(): string
  /** Get the server host */
  host(): string
  /** Get the server port */
  port(): number
  /**
   * Start the Bodhi server
   *
   * # Safety
   * Safe to call from JavaScript/Node.js context via NAPI bindings.
   */
  start(): Promise<void>
  /**
   * Stop the Bodhi server
   *
   * # Safety
   * Safe to call from JavaScript/Node.js context via NAPI bindings.
   */
  stop(): Promise<void>
  /**
   * Check if the server is running
   *
   * # Safety
   * Safe to call from JavaScript/Node.js context via NAPI bindings.
   */
  isRunning(): Promise<boolean>
  /**
   * Get server ping status
   *
   * # Safety
   * Safe to call from JavaScript/Node.js context via NAPI bindings.
   */
  ping(): Promise<boolean>
}
