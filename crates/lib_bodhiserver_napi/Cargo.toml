[package]
name = "lib_bodhiserver_napi"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
lib_bodhiserver = { workspace = true }

dirs = { workspace = true }
log = { workspace = true }
napi = { workspace = true, features = ["async", "serde-json"] }
napi-derive = { workspace = true }
rand = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tempfile = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tracing = { workspace = true }
tracing-appender = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }

[build-dependencies]
napi-build = { workspace = true }

[dev-dependencies]
objs = { workspace = true, features = ["test-utils"] }
services = { workspace = true, features = ["test-utils"] }
lib_bodhiserver = { workspace = true, features = ["test-utils"] }

anyhow = { workspace = true }
rstest = { workspace = true }
tempfile = { workspace = true }
tokio = { workspace = true, features = ["macros", "rt-multi-thread"] }

[features]
test-utils = []
