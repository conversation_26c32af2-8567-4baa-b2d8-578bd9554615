<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Fetch-Tester</title>
  <style>
    body { font-family: sans-serif; max-width: 900px; margin: 2rem auto; }
    label { display: block; margin-top: 1rem; font-weight: 600; }
    input[type="text"], textarea {
      width: 100%; padding: .5rem; font-family: monospace;
    }
    textarea { min-height: 120px; }
    button { margin-top: 1rem; padding: .5rem 1rem; }
    pre { background: #f5f5f5; padding: 1rem; overflow-x: auto; }
  </style>
</head>
<body>
  <h1>Local Fetch Tester</h1>

  <label>Request URL
    <input type="text" id="url" placeholder="http://localhost:1135/api/..." />
  </label>

  <label>Content-Type
    <input type="text" id="ctype" value="application/json" />
  </label>

  <label>Request Body
    <textarea id="body" placeholder='{"example":"data"}'></textarea>
  </label>

  <label>Custom Headers (one per line, "Header: value")
    <textarea id="headers" placeholder="X-Test: 123&#10;Sec-Fetch-Site: same-origin"></textarea>
  </label>

  <label>HTTP Method
    <select id="method">
      <option value="GET" selected>GET</option>
      <option value="POST">POST</option>
      <option value="PUT">PUT</option>
      <option value="PATCH">PATCH</option>
      <option value="DELETE">DELETE</option>
    </select>
  </label>

  <button id="send">Send Request</button>
  <button id="clear" type="button">Clear Output</button>

  <h2>Response</h2>
  <pre id="output">(no response yet)</pre>

<script>
  const $ = id => document.getElementById(id);
  const out = $('output');
  
  // LocalStorage helpers
  const STORAGE_PREFIX = 'tmp--check-xhr-policy-';
  
  function saveValue(key, value) {
    localStorage.setItem(STORAGE_PREFIX + key, value);
  }
  
  function loadValue(key, defaultValue = '') {
    return localStorage.getItem(STORAGE_PREFIX + key) || defaultValue;
  }
  
  function setupPersistence() {
    // Load saved values
    $('url').value = loadValue('url');
    $('ctype').value = loadValue('ctype', 'application/json');
    $('body').value = loadValue('body');
    $('headers').value = loadValue('headers');
    $('method').value = loadValue('method', 'GET');
    
    // Save values on change
    $('url').addEventListener('input', e => saveValue('url', e.target.value));
    $('ctype').addEventListener('input', e => saveValue('ctype', e.target.value));
    $('body').addEventListener('input', e => saveValue('body', e.target.value));
    $('headers').addEventListener('input', e => saveValue('headers', e.target.value));
    $('method').addEventListener('change', e => saveValue('method', e.target.value));
  }
  
  // Initialize persistence when page loads
  document.addEventListener('DOMContentLoaded', setupPersistence);

  function parseHeaders(str) {
    const hdrs = {};
    str.split(/\r?\n/).forEach(line => {
      const idx = line.indexOf(':');
      if (idx > 0) {
        const name  = line.slice(0, idx).trim();
        const value = line.slice(idx + 1).trim();
        if (name) hdrs[name] = value;
      }
    });
    return hdrs;
  }

  $('send').addEventListener('click', async () => {
    const url   = $('url').value.trim();
    const type  = $('ctype').value.trim();
    const body  = $('body').value;
    const method = $('method').value;
    const extra = parseHeaders($('headers').value);

    if (!url) { alert('Please enter a URL'); return; }

    // Build headers object
    const hdrs = { ...extra };
    if (type) hdrs['Content-Type'] = type;
    hdrs['Accept'] = 'application/json';

    out.textContent = 'Fetching…';
    try {
      const resp = await fetch(url, {
        method: method,
        headers: hdrs,
        body: body || undefined,
        credentials: 'omit'
      });

      const text = await resp.text();
      const headers = Array.from(resp.headers.entries())
        .map(([k, v]) => `${k}: ${v}`)
        .join('\n');

      out.textContent =
        `Status: ${resp.status} ${resp.statusText}\n` +
        `\n-- Headers --\n${headers}` +
        `\n\n-- Body --\n${text}`;
    } catch (err) {
      out.textContent = 'Error: ' + err;
    }
  });

  $('clear').addEventListener('click', () => {
    out.textContent = '(no response yet)';
  });
</script>
</body>
</html>
