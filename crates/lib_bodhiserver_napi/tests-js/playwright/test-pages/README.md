# OAuth 2.0 Cross-Client Token Exchange Test Pages

This directory contains HTML+JavaScript test pages that demonstrate and test OAuth 2.0 Token Exchange (RFC 8693) functionality.

## Test Scenario

The test demonstrates a real-world cross-client token exchange scenario:

1. **Third-Party Authentication**: User authenticates with a third-party client (`test-resource-*`)
2. **Token Exchange**: The third-party token is exchanged for our app's client token using RFC 8693
3. **API Access**: The exchanged token is used to make API calls to our server

## Files

- `index.html` - Main page that initiates OAuth flow with third-party client
- `auth/callback/index.html` - OAuth callback handler that exchanges code for token
- `chat.html` - Chat interface that tests API calls with the cross-client token

## Configuration

The test uses these OAuth clients:

- **Third-Party Client**: `test-resource-059a5742-2d7c-44cd-b2fa-2952e8fabee3`
  - Used for initial user authentication
  - Redirect URI: `bodhi.localhost/auth/callback`
  
- **App Client**: `test-client-059a5742-2d7c-44cd-b2fa-2952e8fabee3`
  - Our application's client
  - Receives exchanged tokens via RFC 8693

## Running the Test

### Manual Testing

1. Add `bodhi.localhost` to your `/etc/hosts` file:
   ```
   127.0.0.1 bodhi.localhost
   ```

2. Serve the test pages on `bodhi.localhost`:
   ```bash
   # From this directory
   python3 -m http.server 8080 --bind 127.0.0.1
   ```

3. Open `http://bodhi.localhost:8080/index.html` in your browser

4. Follow the OAuth flow:
   - Click "Login with Third-Party Client"
   - Authenticate with Keycloak
   - Return to callback page
   - Navigate to chat page
   - Test API calls

### Automated Testing

The Playwright test `cross-client-token-exchange.spec.mjs` runs this flow automatically:

```bash
npm test -- cross-client-token-exchange.spec.mjs
```

## What the Test Validates

1. **OAuth Flow**: Complete PKCE-based OAuth 2.0 flow with third-party client
2. **Token Exchange**: Server successfully exchanges third-party token for app token
3. **API Authentication**: Exchanged token works for API calls
4. **Security**: Proper state validation and CSRF protection

## Expected Results

- ✅ User can authenticate with third-party client
- ✅ Token is successfully exchanged (no 401 errors)
- ✅ API calls work with exchanged token
- ✅ Chat API returns appropriate responses

## Troubleshooting

### Common Issues

1. **Redirect URI Mismatch**: Ensure `bodhi.localhost` is in `/etc/hosts`
2. **CORS Errors**: Test pages must be served over HTTP, not opened as files
3. **Token Expired**: Tokens have limited lifetime, re-authenticate if needed
4. **LLM Server Not Running**: API tests may fail if LLM server is not available (this is expected in CI)

### Debug Information

The test pages provide detailed logging:
- Token payload information
- Request/response details
- Error messages with context
- Step-by-step flow progress

## Security Notes

This is a test environment with:
- Test credentials (not production)
- Localhost-only redirect URIs
- Debug logging enabled
- CORS headers for development

Do not use these configurations in production.
