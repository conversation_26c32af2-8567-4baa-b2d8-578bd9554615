[package]
name = "auth_middleware"
version = "0.1.0"
edition = "2021"

[dependencies]
errmeta_derive = { workspace = true }
objs = { workspace = true }
include_dir = { workspace = true }
services = { workspace = true }
server_core = { workspace = true }
time = { workspace = true }

axum = { workspace = true }
chrono = { workspace = true }
jsonwebtoken = { workspace = true }
rand = { workspace = true }
rstest = { workspace = true, optional = true }
serde = { workspace = true, features = ["derive"] }
thiserror = { workspace = true }
serde_json = { workspace = true, features = ["indexmap"] }
tower-sessions = { workspace = true }
tracing = { workspace = true }

[dev-dependencies]
objs = { workspace = true, features = ["test-utils"] }
services = { workspace = true, features = ["test-utils"] }
server_core = { workspace = true, features = ["test-utils"] }

anyhow = { workspace = true }
anyhow_trace = { workspace = true }
mockall = { workspace = true }
rstest = { workspace = true }
tempfile = { workspace = true }
tower = { workspace = true, features = ["util"] }
tokio = { workspace = true, features = ["full"] }
pretty_assertions = { workspace = true }
maplit = { workspace = true }
uuid = { workspace = true }

[features]
test-utils = ["rstest", "services/test-utils"]
