# OAuth 2.1 Token Exchange Security Research

## Executive Summary

This research investigates secure OAuth 2.1 token exchange patterns for preventing privilege escalation when third-party clients exchange tokens for access to our resource server. The key finding is that **scope-limited token exchange with explicit consent mechanisms** is the standard approach, implemented through authorization server policies rather than built-in consent screens.

## Problem Statement

**Current Architecture:**
- Our application acts as an OAuth resource server with its own client ID in Keycloak
- Third-party client applications have separate client IDs in Keycloak  
- Users have unified accounts in Keycloak and can authenticate to both our resource server and third-party clients
- **Security Risk**: When users authenticate to third-party clients, those clients inherit the user's full privilege level on our resource server without explicit consent

**Example Scenario:**
If an admin user logs into a third-party app, that app gains admin-level access to our APIs without the user explicitly approving those specific privileges.

## Research Findings

### 1. OAuth 2.1 Token Exchange Standard (RFC 8693)

**Key Specifications:**
- **RFC 8693**: "OAuth 2.0 Token Exchange" - The foundational standard
- **Scope Parameter**: Allows clients to specify desired scope of the requested security token
- **Audience Parameter**: Logical name of target service where token will be used
- **Resource Parameter**: URI indicating target service or resource

**Security Considerations from RFC 8693:**
- Authorization servers MUST perform appropriate validation procedures
- Token exchange should not impact validity of original tokens
- Scope limitations are explicitly supported through the `scope` parameter
- Authorization servers can apply policy based on target audience/resource

### 2. Standard Security Patterns

#### A. Scope-Limited Token Exchange
**Implementation:**
```http
POST /auth/realms/master/protocol/openid-connect/token
Content-Type: application/x-www-form-urlencoded

grant_type=urn:ietf:params:oauth:grant-type:token-exchange
&client_id=third-party-client
&client_secret=secret123
&subject_token=eyJhbGciOiJSUzI1...
&requested_token_type=urn:ietf:params:oauth:token-type:access_token
&audience=our-resource-server
&scope=read:basic write:limited
```

**Benefits:**
- Limits exchanged token to specific scopes
- Prevents full privilege inheritance
- Standard OAuth 2.1 compliant approach

#### B. Sidecar Proxy Pattern (Raiffeisen Bank Implementation)
**Architecture:**
- Token exchange performed by sidecar proxy upstream of API
- Transparent to both consumer and API provider
- Caches exchanged tokens for performance
- Applies authorization policies based on target service

**Key Quote from Implementation:**
> "Token exchange can serve as an elegant solution, serving each application the relevant Authorization metadata while maintaining Identity and Authentication context, preventing privilege escalation and keeping a lid on token size."

**Source:** [Token exchange proxy solution pattern for tailored access token authorization payload](https://medium.com/@yaron.zehavi/token-exchange-proxy-solution-pattern-for-tailored-access-token-authorization-payload-e4411e328c0a)

### 3. Keycloak-Specific Capabilities

#### Authorization Services
- **Fine-grained permissions**: Resource-based authorization with scopes
- **Policy enforcement**: Client permissions and scope mappings
- **Token exchange permissions**: Automatic permissions created when enabling authorization services

#### Token Exchange Configuration
- **Client permissions**: Control which clients can exchange tokens
- **Scope mapping**: Map third-party client scopes to resource server roles
- **Audience restrictions**: Limit token exchange to specific target services

**Reference:** [Keycloak Authorization Services Guide](https://www.keycloak.org/docs/latest/authorization_services/index.html)

### 4. Consent Screen Limitations

**Key Finding:** OAuth 2.1 and Keycloak do **not** provide built-in consent screens for token exchange. Consent is typically handled during the initial authorization flow, not during token exchange.

**Alternative Approaches:**
1. **Pre-authorization consent**: Users consent to specific scopes during initial third-party client authentication
2. **Policy-based limitations**: Authorization server enforces scope restrictions based on client configuration
3. **Custom consent flows**: Implement application-specific consent mechanisms before token exchange

## Recommended Implementation Strategy

### Phase 1: Scope-Limited Token Exchange

**Implementation Steps:**
1. **Configure Keycloak client permissions** for third-party clients
2. **Define scope mappings** between third-party client permissions and our resource server roles
3. **Implement token exchange endpoint** with scope limitations
4. **Apply audience restrictions** to limit token usage to our resource server

**Example Configuration:**
```json
{
  "client_id": "third-party-app",
  "allowed_scopes": ["read:basic", "write:limited"],
  "resource_server_mappings": {
    "read:basic": "resource_user",
    "write:limited": "resource_user"
  },
  "max_scope_elevation": false
}
```

### Phase 2: Enhanced Security Controls

**Additional Measures:**
1. **Token exchange audit logging** for security monitoring
2. **Rate limiting** on token exchange requests
3. **Time-limited tokens** with shorter expiration for exchanged tokens
4. **Revocation mechanisms** for compromised tokens

### Phase 3: Custom Consent (Optional)

**If explicit consent is required:**
1. **Pre-exchange consent flow**: Redirect users to consent screen before token exchange
2. **Consent storage**: Track user consent decisions for future exchanges
3. **Consent revocation**: Allow users to revoke previously granted permissions

## Security Trade-offs Analysis

| Approach | Security Level | Implementation Complexity | User Experience |
|----------|---------------|---------------------------|-----------------|
| Scope-Limited Exchange | High | Medium | Transparent |
| Sidecar Proxy | Very High | High | Transparent |
| Custom Consent | Highest | Very High | Additional friction |
| Policy-Only | Medium | Low | Transparent |

## Keycloak Configuration for BodhiApp Architecture

### System Configuration Details

**Keycloak Realm**: `bodhi`
**Resource Server Client**: `resource-xyz` (confidential, with client secret)
**Third-party Client**: `client-abc` (non-confidential, PKCE-enabled)
**Upstream Resource Server**: `resource-lmn` (confidential, with client secret)

### Scope Configuration Strategy

Based on the existing BodhiApp role and token scope system, here's the recommended approach:

#### 1. Scope Definition Strategy

**Recommendation**: Use **role-equivalent scopes** that directly map to existing roles and token scopes.

**Rationale**:
- Simplifies integration with existing authorization middleware
- Maintains consistency with current `Role` and `TokenScope` enums
- Reduces complexity in scope-to-role mapping logic

**Proposed Scopes**:
```
scope_user_user       -> Role::User + TokenScope::User
scope_user_power      -> Role::PowerUser + TokenScope::PowerUser
scope_user_manager    -> Role::Manager + TokenScope::Manager
scope_user_admin      -> Role::Admin + TokenScope::Admin
```

#### 2. Scope Level Configuration

**Recommendation**: **Realm-level client scopes** with client-specific assignments.

**Implementation**:
1. **Create realm-level client scopes** for each role level
2. **Assign scopes to clients** based on their intended privilege levels
3. **Enable consent** for third-party clients to show users what access they're granting

**Keycloak Configuration Steps**:

```bash
# 1. Create realm-level client scopes
kcadm.sh create client-scopes -r bodhi \
  -s name=scope_user_user \
  -s description="Basic user access to resource server" \
  -s protocol=openid-connect \
  -s attributes.consent.screen.text="Basic user access"

kcadm.sh create client-scopes -r bodhi \
  -s name=scope_user_power \
  -s description="Power user access to resource server" \
  -s protocol=openid-connect \
  -s attributes.consent.screen.text="Power user access"

kcadm.sh create client-scopes -r bodhi \
  -s name=scope_user_manager \
  -s description="Manager access to resource server" \
  -s protocol=openid-connect \
  -s attributes.consent.screen.text="Manager access"

kcadm.sh create client-scopes -r bodhi \
  -s name=scope_user_admin \
  -s description="Admin access to resource server" \
  -s protocol=openid-connect \
  -s attributes.consent.screen.text="Admin access"
```

#### 3. Client Scope Assignments

**Third-party Client (`client-abc`)**:
```bash
# Assign optional scopes to third-party client
kcadm.sh update clients/client-abc -r bodhi \
  -s 'optionalClientScopes=["scope_user_user","scope_user_power"]'

# Enable consent for third-party client
kcadm.sh update clients/client-abc -r bodhi \
  -s consentRequired=true
```

**Resource Server Client (`resource-xyz`)**:
```bash
# Assign all scopes as default for resource server
kcadm.sh update clients/resource-xyz -r bodhi \
  -s 'defaultClientScopes=["scope_user_user","scope_user_power","scope_user_manager","scope_user_admin"]'

# Enable token exchange for resource server
kcadm.sh update clients/resource-xyz -r bodhi \
  -s attributes.oauth2.device.authorization.grant.enabled=false \
  -s attributes.token.exchange.standard.enabled=true
```

#### 4. Token Exchange Implementation

**Dual Token Exchange Scenarios**:

**Scenario A: Third-party to Resource Server**
```http
POST /realms/bodhi/protocol/openid-connect/token
Authorization: Basic Y2xpZW50LWFiYzpzZWNyZXQ=
Content-Type: application/x-www-form-urlencoded

grant_type=urn:ietf:params:oauth:grant-type:token-exchange
&subject_token=eyJhbGciOiJSUzI1NiIs...
&subject_token_type=urn:ietf:params:oauth:token-type:access_token
&audience=resource-xyz
&scope=scope_user_user
```

**Scenario B: Resource Server to Upstream**
```http
POST /realms/bodhi/protocol/openid-connect/token
Authorization: Basic cmVzb3VyY2UteHl6OnNlY3JldA==
Content-Type: application/x-www-form-urlencoded

grant_type=urn:ietf:params:oauth:grant-type:token-exchange
&subject_token=eyJhbGciOiJSUzI1NiIs...
&subject_token_type=urn:ietf:params:oauth:token-type:access_token
&audience=resource-lmn
&scope=scope_user_power
```

### Integration with Existing Authorization System

#### 1. Scope-to-Role Mapping

**Proposed UserScope Enum** (similar to TokenScope):
```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum UserScope {
  #[strum(serialize = "scope_user_user")]
  User,
  #[strum(serialize = "scope_user_power")]
  PowerUser,
  #[strum(serialize = "scope_user_manager")]
  Manager,
  #[strum(serialize = "scope_user_admin")]
  Admin,
}

impl UserScope {
  pub fn to_role(&self) -> Role {
    match self {
      UserScope::User => Role::User,
      UserScope::PowerUser => Role::PowerUser,
      UserScope::Manager => Role::Manager,
      UserScope::Admin => Role::Admin,
    }
  }

  pub fn to_token_scope(&self) -> TokenScope {
    match self {
      UserScope::User => TokenScope::User,
      UserScope::PowerUser => TokenScope::PowerUser,
      UserScope::Manager => TokenScope::Manager,
      UserScope::Admin => TokenScope::Admin,
    }
  }
}
```

#### 2. Middleware Integration

**Enhanced Authorization Middleware**:
```rust
// In api_auth_middleware.rs
pub async fn api_auth_middleware(
  required_role: Role,
  required_scope: Option<TokenScope>,
  State(state): State<Arc<dyn RouterState>>,
  req: Request,
  next: Next,
) -> Result<Response, ApiError> {
  // Check for exchanged token scope header
  let user_scope_header = req.headers().get("X-User-Scope");

  match (role_header, scope_header, user_scope_header, required_scope) {
    // Existing role-based auth
    (Some(role_header), _, _, _) => {
      // ... existing role validation
    }

    // Existing token scope auth
    (None, Some(scope_header), None, Some(required_scope)) => {
      // ... existing token scope validation
    }

    // NEW: User scope from token exchange
    (None, None, Some(user_scope_header), _) => {
      let user_scope = user_scope_header
        .to_str()
        .map_err(|e| ApiAuthError::MalformedScope(e.to_string()))?
        .parse::<UserScope>()?;

      let user_role = user_scope.to_role();
      if !user_role.has_access_to(&required_role) {
        return Err(ApiAuthError::Forbidden);
      }
    }

    _ => return Err(ApiAuthError::MissingAuth),
  }

  Ok(next.run(req).await)
}
```

### Consent Screen Configuration

#### 1. Enable Consent for Third-party Clients

**Keycloak Configuration**:
```bash
# Enable consent requirement
kcadm.sh update clients/client-abc -r bodhi \
  -s consentRequired=true

# Configure consent screen text for each scope
kcadm.sh update client-scopes/scope_user_user -r bodhi \
  -s 'attributes.consent.screen.text="Basic access to your BodhiApp data"'

kcadm.sh update client-scopes/scope_user_power -r bodhi \
  -s 'attributes.consent.screen.text="Enhanced access to your BodhiApp data"'
```

#### 2. Consent Flow Integration

**User Experience**:
1. User authenticates to third-party client (`client-abc`)
2. Keycloak displays consent screen showing requested scopes
3. User approves specific scope level (e.g., `scope_user_user`)
4. Third-party client receives token with limited scope
5. Token exchange to resource server maintains scope limitation

### Security Considerations

#### 1. Scope Validation

**Token Exchange Validation**:
- Exchanged tokens cannot have higher privileges than original token
- Audience parameter restricts token usage to specific resource servers
- Scope parameter limits privileges within the target resource server

#### 2. Audit Logging

**Enhanced Logging**:
```rust
// Log token exchange events
log::info!(
  "Token exchange: client={} subject_scope={} requested_scope={} audience={}",
  client_id,
  subject_scope,
  requested_scope,
  audience
);
```

## Implementation Recommendations

### Immediate Actions (High Priority)
1. **Implement scope-limited token exchange** using Keycloak's standard token exchange V2
2. **Create realm-level client scopes** mapping to existing Role/TokenScope enums
3. **Configure consent screens** for third-party clients
4. **Enhance authorization middleware** to handle UserScope from exchanged tokens

### Medium-term Enhancements
1. **Implement comprehensive audit logging** for token exchange operations
2. **Add scope validation** in token exchange requests
3. **Create monitoring dashboards** for token exchange patterns

### Long-term Considerations
1. **Evaluate fine-grained permissions** if more complex authorization scenarios emerge
2. **Consider token caching** for performance optimization in high-volume scenarios
3. **Implement automated scope management** for dynamic client registration

## Conclusion

The recommended approach leverages **Keycloak's standard token exchange V2** with **realm-level client scopes** that directly map to BodhiApp's existing role system. This provides:

- **Security**: Scope-limited token exchange prevents privilege escalation
- **Simplicity**: Direct mapping to existing Role/TokenScope enums
- **User Control**: Consent screens for third-party client access
- **Compliance**: Full OAuth 2.1 RFC 8693 compliance
- **Integration**: Seamless integration with existing authorization middleware

The key insight is using **role-equivalent scopes** (`scope_user_admin`) rather than fine-grained scopes (`read:basic`), which aligns perfectly with BodhiApp's existing authorization model and simplifies the implementation.

## References

1. [RFC 8693 - OAuth 2.0 Token Exchange](https://datatracker.ietf.org/doc/html/rfc8693)
2. [Token exchange proxy solution pattern - Raiffeisen Bank](https://medium.com/@yaron.zehavi/token-exchange-proxy-solution-pattern-for-tailored-access-token-authorization-payload-e4411e328c0a)
3. [Keycloak Authorization Services Guide](https://www.keycloak.org/docs/latest/authorization_services/index.html)
4. [Keycloak Token Exchange Documentation](https://www.keycloak.org/securing-apps/token-exchange)
5. [OAuth 2.0 Security Best Current Practice](https://www.ietf.org/archive/id/draft-ietf-oauth-security-topics-29.html)
