1
00:00:00,000 --> 00:00:01,500
Hey there!

2
00:00:02,000 --> 00:00:05,500
Whether you're a solo developer or running 
a tech team of 50 people,

3
00:00:06,000 --> 00:00:09,500
you've probably faced the same AI challenge:
scattered tools and rising costs.

4
00:00:10,000 --> 00:00:13,500
Your team uses ChatGPT for one thing,
<PERSON> for another, local models for privacy,

5
00:00:14,000 --> 00:00:17,500
and suddenly you've lost track of who's 
spending what on which AI service.

6
00:00:18,000 --> 00:00:20,500
No usage insights, no centralized control.

7
00:00:21,000 --> 00:00:23,000
That's exactly why we built Bodhi App.

8
00:00:23,500 --> 00:00:27,500
Bodhi App is your complete AI stack -
whether you're a team of one or one hundred.

9
00:00:28,000 --> 00:00:31,500
Run it locally on your laptop,
deploy it on your company network,

10
00:00:32,000 --> 00:00:34,000
or spin it up as a Docker service.

11
00:00:34,500 --> 00:00:38,000
Local models for privacy, remote APIs 
with your own keys for power,

12
00:00:38,500 --> 00:00:41,500
role-based access for your team,
and complete usage visibility.

13
00:00:42,000 --> 00:00:45,500
Plus, we're building in MCP support
for centralized agentic workflows -

14
00:00:46,000 --> 00:00:49,000
so your AI agents can discover and connect
to tools automatically.

15
00:00:49,500 --> 00:00:51,500
One platform, all your AI needs covered.

16
00:00:52,000 --> 00:00:55,500
We'll be at AI Agentic Summit Bangalore
on July 2nd - come see us in action!

17
00:00:56,000 --> 00:00:58,500
Bring your AI chaos, 
we'll show you how to organize it. 